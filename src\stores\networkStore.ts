import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface NetworkState {
  isOnline: boolean
  isPrivateMode: boolean
  localModelsAvailable: boolean
  ollamaConnected: boolean
  lmStudioConnected: boolean
  
  // Actions
  toggleOnline: () => void
  togglePrivateMode: () => void
  setLocalModelsAvailable: (available: boolean) => void
  setOllamaConnected: (connected: boolean) => void
  setLmStudioConnected: (connected: boolean) => void
  checkLocalModels: () => Promise<void>
}

export const useNetworkStore = create<NetworkState>()(
  persist(
    (set, get) => ({
      // Initial state
      isOnline: true,
      isPrivateMode: false,
      localModelsAvailable: false,
      ollamaConnected: false,
      lmStudioConnected: false,

      // Toggle network connectivity
      toggleOnline: () => {
        const currentState = get()
        const newOnlineState = !currentState.isOnline
        
        set({ isOnline: newOnlineState })
        
        // If going offline, enable private mode
        if (!newOnlineState) {
          set({ isPrivateMode: true })
        }
      },

      // Toggle private mode
      togglePrivateMode: () => {
        const currentState = get()
        const newPrivateMode = !currentState.isPrivateMode
        
        set({ isPrivateMode: newPrivateMode })
        
        // If enabling private mode, force offline
        if (newPrivateMode) {
          set({ isOnline: false })
        }
      },

      // Set local models availability
      setLocalModelsAvailable: (available: boolean) => {
        set({ localModelsAvailable: available })
      },

      // Set Ollama connection status
      setOllamaConnected: (connected: boolean) => {
        set({ ollamaConnected: connected })
        // Update overall local models availability
        const { lmStudioConnected } = get()
        set({ localModelsAvailable: connected || lmStudioConnected })
      },

      // Set LM Studio connection status
      setLmStudioConnected: (connected: boolean) => {
        set({ lmStudioConnected: connected })
        // Update overall local models availability
        const { ollamaConnected } = get()
        set({ localModelsAvailable: connected || ollamaConnected })
      },

      // Check for local model providers
      checkLocalModels: async () => {
        try {
          // Check Ollama (default port 11434)
          const ollamaResponse = await fetch('http://localhost:11434/api/tags', {
            method: 'GET',
            signal: AbortSignal.timeout(3000) // 3 second timeout
          })
          
          if (ollamaResponse.ok) {
            const ollamaData = await ollamaResponse.json()
            const hasModels = ollamaData.models && ollamaData.models.length > 0
            get().setOllamaConnected(hasModels)
          } else {
            get().setOllamaConnected(false)
          }
        } catch (error) {
          get().setOllamaConnected(false)
        }

        try {
          // Check LM Studio (default port 1234)
          const lmStudioResponse = await fetch('http://localhost:1234/v1/models', {
            method: 'GET',
            signal: AbortSignal.timeout(3000) // 3 second timeout
          })
          
          if (lmStudioResponse.ok) {
            const lmStudioData = await lmStudioResponse.json()
            const hasModels = lmStudioData.data && lmStudioData.data.length > 0
            get().setLmStudioConnected(hasModels)
          } else {
            get().setLmStudioConnected(false)
          }
        } catch (error) {
          get().setLmStudioConnected(false)
        }
      }
    }),
    {
      name: 'network-storage',
      partialize: (state) => ({
        isOnline: state.isOnline,
        isPrivateMode: state.isPrivateMode
      })
    }
  )
)
