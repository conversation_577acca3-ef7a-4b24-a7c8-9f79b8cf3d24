import React from 'react'
import { useAppStore } from '../../../store'
import { Artifact } from '../../../types'

export function ArtifactTabs() {
  const { 
    artifacts: { artifacts, currentArtifact },
    setActiveArtifact,
    removeArtifact
  } = useAppStore()

  if (artifacts.length <= 1) return null

  const handleTabClick = (artifactId: string) => {
    setActiveArtifact(artifactId)
  }

  const handleCloseTab = (e: React.MouseEvent, artifactId: string) => {
    e.stopPropagation()
    removeArtifact(artifactId)
  }

  return (
    <div className="flex items-center bg-neutral-800/50 border-b border-neutral-700 relative">
      {/* Scrollable tabs container */}
      <div className="flex overflow-x-auto artifact-tabs-scroll">
        <div className="flex">
          {artifacts.map((artifact) => (
            <ArtifactTab
              key={artifact.id}
              artifact={artifact}
              isActive={currentArtifact?.id === artifact.id}
              onClick={() => handleTabClick(artifact.id)}
              onClose={(e) => handleCloseTab(e, artifact.id)}
            />
          ))}
        </div>
      </div>

      {/* Overflow indicator */}
      {artifacts.length > 4 && (
        <>
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-neutral-800/50 to-transparent pointer-events-none" />
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-neutral-400 bg-neutral-800 px-1 rounded">
            +{artifacts.length - 3}
          </div>
        </>
      )}
    </div>
  )
}

interface ArtifactTabProps {
  artifact: Artifact
  isActive: boolean
  onClick: () => void
  onClose: (e: React.MouseEvent) => void
}

function ArtifactTab({ artifact, isActive, onClick, onClose }: ArtifactTabProps) {
  const getArtifactIcon = (type: Artifact['type']) => {
    switch (type) {
      case 'code':
      case 'json':
        return '💻'
      case 'image':
        return '🖼️'
      case 'markdown':
        return '📝'
      case 'mermaid':
        return '📊'
      case 'html':
        return '🌐'
      default:
        return '📄'
    }
  }

  const tabClasses = `
    flex items-center space-x-2 px-3 py-2 min-w-0 max-w-40 cursor-pointer
    border-r border-neutral-700 transition-colors relative group flex-shrink-0
    ${isActive
      ? 'bg-neutral-700 text-white border-b-2 border-indigo-500'
      : 'text-neutral-400 hover:text-white hover:bg-neutral-700/50'
    }
  `

  return (
    <div className={tabClasses} onClick={onClick}>
      {/* Icon */}
      <span className="text-sm flex-shrink-0">
        {getArtifactIcon(artifact.type)}
      </span>

      {/* Title */}
      <span className="truncate text-sm font-medium">
        {artifact.title}
      </span>

      {/* Language/Type indicator */}
      {artifact.metadata.language && (
        <span className="text-xs text-neutral-500 flex-shrink-0">
          {artifact.metadata.language}
        </span>
      )}

      {/* Close button */}
      <button
        onClick={onClose}
        className="flex-shrink-0 p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-neutral-600 transition-all"
        title="Close artifact"
      >
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      {/* Active indicator */}
      {isActive && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-indigo-500" />
      )}
    </div>
  )
}
