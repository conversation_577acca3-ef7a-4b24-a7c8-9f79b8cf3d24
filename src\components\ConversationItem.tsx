import React from 'react';
import { MessageSquare, Edit3, Trash2, Pin } from './Icons';
import { Conversation } from '../types';

interface ConversationItemProps {
  conversation: Conversation;
  currentConversationId: string | null;
  handleSelectConversation: (id: string) => void;
  handleEditStart: (id: string, title: string, e: React.MouseEvent) => void;
  handleDeleteConversation: (id: string, e: React.MouseEvent) => void;
  editingId: string | null;
  editTitle: string;
  handleEditSave: (id: string) => Promise<void>;
  handleEditCancel: () => void;
  setEditTitle: (title: string) => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  currentConversationId,
  handleSelectConversation,
  handleEditStart,
  handleDeleteConversation,
  editingId,
  editTitle,
  handleEditSave,
  handleEditCancel,
  setEditTitle,
}) => {
  return (
    <div
      key={conversation.id}
      className={`
        group relative flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium cursor-pointer transition-colors
        ${currentConversationId === conversation.id 
          ? 'bg-neutral-800 text-white' 
          : 'hover:bg-neutral-800/50 text-neutral-300'
        }
      `}
      onClick={() => handleSelectConversation(conversation.id)}
    >
      <MessageSquare className="h-4 w-4 shrink-0" />
      
      {editingId === conversation.id ? (
        <input
          type="text"
          value={editTitle}
          onChange={(e) => setEditTitle(e.target.value)}
          onBlur={() => handleEditSave(conversation.id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleEditSave(conversation.id);
            if (e.key === 'Escape') handleEditCancel();
          }}
          className="flex-1 bg-transparent border-none outline-none text-sm"
          autoFocus
          onClick={(e) => e.stopPropagation()}
        />
      ) : (
        <span className="flex-1 truncate">{conversation.title}</span>
      )}

      {conversation.is_pinned === 1 && (
        <Pin className="h-3 w-3 text-indigo-400 absolute right-16" />
      )}

      {/* Action buttons */}
      <div className="opacity-0 group-hover:opacity-100 flex items-center gap-1 transition-opacity">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleEditStart(conversation.id, conversation.title, e);
          }}
          className="p-1 hover:bg-neutral-700 rounded"
        >
          <Edit3 className="h-3 w-3" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteConversation(conversation.id, e);
          }}
          className="p-1 hover:bg-neutral-700 rounded text-red-400"
        >
          <Trash2 className="h-3 w-3" />
        </button>
      </div>
    </div>
  );
};

export default ConversationItem;
