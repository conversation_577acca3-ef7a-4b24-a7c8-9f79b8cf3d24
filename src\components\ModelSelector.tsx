import React, { useState, useMemo } from 'react'
import { useAppStore } from '../store'
import { Search, Star, Brain, Code, Eye, Gift, ChevronDown, Check } from './Icons'
import { 
  modelCategories, 
  enhanceModelInfo, 
  searchModels, 
  sortModels, 
  getRecommendedModels,
  formatPricing,
  formatContextLength 
} from '../utils/modelUtils'
import { OpenRouterModel } from '../types'

interface ModelSelectorProps {
  selectedModel?: string
  onModelSelect: (modelId: string) => void
  className?: string
}

const ModelSelector: React.FC<ModelSelectorProps> = ({ 
  selectedModel, 
  onModelSelect, 
  className = '' 
}) => {
  const { models } = useAppStore()
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy] = useState<'name' | 'provider' | 'price' | 'context'>('name')

  // Filter and search models
  const filteredModels = useMemo(() => {
    let filtered = models

    // Apply category filter
    const category = modelCategories.find(cat => cat.id === selectedCategory)
    if (category) {
      filtered = filtered.filter(category.filter)
    }

    // Apply search
    if (searchQuery.trim()) {
      filtered = searchModels(filtered, searchQuery)
    }

    // Sort models
    return sortModels(filtered, sortBy)
  }, [models, selectedCategory, searchQuery, sortBy])

  // Get recommended models for quick access
  const recommendedModels = useMemo(() => {
    return getRecommendedModels(models).slice(0, 4)
  }, [models])

  const selectedModelInfo = models.find(m => m.id === selectedModel)

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'free': return <Gift className="h-4 w-4" />
      case 'flagship': return <Star className="h-4 w-4" />
      case 'reasoning': return <Brain className="h-4 w-4" />
      case 'code': return <Code className="h-4 w-4" />
      case 'vision': return <Eye className="h-4 w-4" />
      default: return null
    }
  }

  const ModelCard: React.FC<{ model: OpenRouterModel; isSelected: boolean }> = ({ model, isSelected }) => {
    const enhanced = enhanceModelInfo(model)
    
    return (
      <div
        className={`
          p-3 rounded-lg border cursor-pointer transition-all hover:bg-neutral-800/50
          ${isSelected 
            ? 'border-indigo-500 bg-indigo-500/10' 
            : 'border-neutral-700 hover:border-neutral-600'
          }
        `}
        onClick={() => {
          onModelSelect(model.id)
          setIsOpen(false)
        }}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate">{model.name}</h4>
            <p className="text-xs text-neutral-400">{enhanced.provider}</p>
          </div>
          <div className="flex items-center gap-1 ml-2">
            {enhanced.isFree && <div title="Free"><Gift className="h-3 w-3 text-green-400" /></div>}
            {enhanced.isFlagship && <div title="Flagship"><Star className="h-3 w-3 text-yellow-400" /></div>}
            {enhanced.isReasoning && <div title="Reasoning"><Brain className="h-3 w-3 text-purple-400" /></div>}
            {enhanced.isCode && <div title="Code"><Code className="h-3 w-3 text-blue-400" /></div>}
            {enhanced.isVision && <div title="Vision"><Eye className="h-3 w-3 text-indigo-400" /></div>}
            {isSelected && <Check className="h-3 w-3 text-indigo-400" />}
          </div>
        </div>
        
        <div className="flex items-center justify-between text-xs text-neutral-500">
          <span>{formatContextLength(model.context_length)} context</span>
          <span>{formatPricing(model)}</span>
        </div>
        
        {model.description && (
          <p className="text-xs text-neutral-400 mt-1 line-clamp-2">{model.description}</p>
        )}
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="input-field w-full flex items-center justify-between"
      >
        <div className="flex items-center gap-2 min-w-0">
          {selectedModelInfo ? (
            <>
              <div className="flex items-center gap-1">
                {enhanceModelInfo(selectedModelInfo).isFree && <Gift className="h-3 w-3 text-green-400" />}
                {enhanceModelInfo(selectedModelInfo).isFlagship && <Star className="h-3 w-3 text-yellow-400" />}
              </div>
              <span className="truncate">{selectedModelInfo.name}</span>
            </>
          ) : (
            <span className="text-neutral-500">Select a model...</span>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-neutral-900 border border-neutral-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-3 border-b border-neutral-700">
            {/* Search */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
              <input
                type="text"
                placeholder="Search models..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-neutral-800 border border-neutral-600 rounded-lg text-sm focus:outline-none focus:border-indigo-500"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-1">
              {modelCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`
                    px-2 py-1 rounded text-xs font-medium transition-colors flex items-center gap-1
                    ${selectedCategory === category.id
                      ? 'bg-indigo-500 text-white'
                      : 'bg-neutral-800 text-neutral-300 hover:bg-neutral-700'
                    }
                  `}
                >
                  {getCategoryIcon(category.id)}
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="max-h-64 overflow-y-auto">
            {/* Recommended Models (only show when no search/filter) */}
            {!searchQuery && selectedCategory === 'all' && recommendedModels.length > 0 && (
              <div className="p-3 border-b border-neutral-700">
                <h3 className="text-sm font-medium mb-2 text-neutral-300">Recommended</h3>
                <div className="grid grid-cols-1 gap-2">
                  {recommendedModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* All Models */}
            <div className="p-3">
              {filteredModels.length === 0 ? (
                <div className="text-center py-4 text-neutral-400">
                  <p>No models found</p>
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="text-indigo-400 hover:text-indigo-300 text-sm mt-1"
                    >
                      Clear search
                    </button>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default ModelSelector
