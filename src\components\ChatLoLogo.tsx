import React from 'react'

interface ChatLoLogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'full' | 'icon' | 'text'
}

const ChatLoLogo: React.FC<ChatLoLogoProps> = ({ 
  className = '', 
  size = 'md',
  variant = 'full' 
}) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8', 
    lg: 'h-12',
    xl: 'h-16'
  }

  if (variant === 'icon') {
    return (
      <div className={`${sizeClasses[size]} ${className}`}>
        <svg viewBox="0 0 531 531" className="h-full w-auto">
          {/* ChatLo Icon - Heart and Chat Elements */}
          <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
            <path d="M597.169,438.935C593.612,408.128 588.274,377.308 583.975,346.577C582.144,333.487 578.24,320.52 576.729,307.361C575.436,296.109 578.123,291.06 589.493,288.594C601.167,287.931 612.214,289.183 623.854,289.935L670.29,292.724C720.473,295.59 772.3,297.894 822.27,304.512C827.681,305.229 831.605,308.51 832.051,314.161C833.287,329.824 833.538,345.597 833.975,361.323L836.035,456.328C818.46,455.99 800.783,455.253 783.204,454.642C795.953,432.474 800.462,409.693 790.28,385.366C776.577,356.149 737.093,347.891 712.111,368.35C705.819,373.502 701.734,381.979 698.423,389.277C693.461,379.423 685.989,369.587 675.641,365.089C651.972,354.801 621.603,365.998 611.66,390.161C603.911,408.994 609.763,421.839 616.585,439.274C610.429,439.363 603.389,439.062 597.169,438.935Z" fill="url(#_Linear1)" fillRule="nonzero"/>
          </g>
          <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
            <path d="M666.294,493.434C647.201,476.815 633.479,462.028 622.3,438.574C612.477,419.498 608.529,394.064 626.391,378.416C650.298,357.471 684.755,364.08 694.705,395.385C695.441,397.7 697.121,400.004 699.424,400.86C707.794,399.09 699.986,368.429 737.856,363.178C744.996,361.865 756.487,363.634 763.212,366.485C790.226,377.935 795.858,414.893 784.994,439.234C773.045,466.003 750.011,488.08 728.618,507.398C722.058,513.322 714.361,522.872 706.243,526.124C692.394,512.513 680.69,505.457 666.294,493.434Z" fill="#FF8383" fillRule="nonzero"/>
          </g>
          <defs>
            <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-89.6814,-227.584,227.584,-89.6814,757.825,485.214)">
              <stop offset="0" stopColor="#D5D8E0" stopOpacity="1"/>
              <stop offset="1" stopColor="#89AFBA" stopOpacity="1"/>
            </linearGradient>
          </defs>
        </svg>
      </div>
    )
  }

  if (variant === 'text') {
    return (
      <div className={`flex items-center ${className}`}>
        <span className="text-2xl font-bold">
          <span className="text-supplement1">Chat</span>
          <span className="text-secondary">Lo</span>
        </span>
      </div>
    )
  }

  // Full logo with icon and text
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className={sizeClasses[size]}>
        <svg viewBox="0 0 531 531" className="h-full w-auto">
          {/* ChatLo Icon */}
          <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
            <path d="M597.169,438.935C593.612,408.128 588.274,377.308 583.975,346.577C582.144,333.487 578.24,320.52 576.729,307.361C575.436,296.109 578.123,291.06 589.493,288.594C601.167,287.931 612.214,289.183 623.854,289.935L670.29,292.724C720.473,295.59 772.3,297.894 822.27,304.512C827.681,305.229 831.605,308.51 832.051,314.161C833.287,329.824 833.538,345.597 833.975,361.323L836.035,456.328C818.46,455.99 800.783,455.253 783.204,454.642C795.953,432.474 800.462,409.693 790.28,385.366C776.577,356.149 737.093,347.891 712.111,368.35C705.819,373.502 701.734,381.979 698.423,389.277C693.461,379.423 685.989,369.587 675.641,365.089C651.972,354.801 621.603,365.998 611.66,390.161C603.911,408.994 609.763,421.839 616.585,439.274C610.429,439.363 603.389,439.062 597.169,438.935Z" fill="url(#_Linear1)" fillRule="nonzero"/>
          </g>
          <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
            <path d="M666.294,493.434C647.201,476.815 633.479,462.028 622.3,438.574C612.477,419.498 608.529,394.064 626.391,378.416C650.298,357.471 684.755,364.08 694.705,395.385C695.441,397.7 697.121,400.004 699.424,400.86C707.794,399.09 699.986,368.429 737.856,363.178C744.996,361.865 756.487,363.634 763.212,366.485C790.226,377.935 795.858,414.893 784.994,439.234C773.045,466.003 750.011,488.08 728.618,507.398C722.058,513.322 714.361,522.872 706.243,526.124C692.394,512.513 680.69,505.457 666.294,493.434Z" fill="#FF8383" fillRule="nonzero"/>
          </g>
          <defs>
            <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-89.6814,-227.584,227.584,-89.6814,757.825,485.214)">
              <stop offset="0" stopColor="#D5D8E0" stopOpacity="1"/>
              <stop offset="1" stopColor="#89AFBA" stopOpacity="1"/>
            </linearGradient>
          </defs>
        </svg>
      </div>
      <span className="text-xl font-bold">
        <span className="text-supplement1">Chat</span>
        <span className="text-secondary">Lo</span>
      </span>
    </div>
  )
}

export default ChatLoLogo
