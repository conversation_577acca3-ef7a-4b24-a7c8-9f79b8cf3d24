import React, { useState, useRef } from 'react'
import { Artifact } from '../../../types'

interface HtmlArtifactViewerProps {
  artifact: Artifact
}

export function HtmlArtifactViewer({ artifact }: HtmlArtifactViewerProps) {
  const [viewMode, setViewMode] = useState<'preview' | 'source'>('preview')
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      console.log('HTML copied to clipboard')
    } catch (error) {
      console.error('Failed to copy HTML:', error)
    }
  }

  const handleDownload = () => {
    const blob = new Blob([artifact.content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleOpenInNewTab = () => {
    const blob = new Blob([artifact.content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
    // Clean up the URL after a delay
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }

  const handleRefresh = () => {
    if (iframeRef.current) {
      const iframe = iframeRef.current
      const blob = new Blob([artifact.content], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      iframe.src = url
      
      // Clean up previous URL
      iframe.onload = () => {
        URL.revokeObjectURL(url)
      }
    }
  }

  return (
    <div className="h-full flex flex-col bg-neutral-900">
      {/* Toolbar */}
      <div className="flex-shrink-0 flex items-center justify-between p-3 bg-neutral-800 border-b border-neutral-700">
        <div className="flex items-center space-x-3">
          {/* View mode toggle */}
          <div className="flex bg-neutral-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('preview')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'preview'
                  ? 'bg-indigo-600 text-white'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              🌐 Preview
            </button>
            <button
              onClick={() => setViewMode('source')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'source'
                  ? 'bg-indigo-600 text-white'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              📝 Source
            </button>
          </div>

          <span className="text-xs text-neutral-500">
            HTML Document
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Refresh button (for preview mode) */}
          {viewMode === 'preview' && (
            <button
              onClick={handleRefresh}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Refresh preview"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}

          {/* Copy button */}
          <button
            onClick={handleCopy}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Copy HTML"
          >
            📋 Copy
          </button>

          {/* Open in new tab button */}
          <button
            onClick={handleOpenInNewTab}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Open in new tab"
          >
            🔗 Open
          </button>

          {/* Download button */}
          <button
            onClick={handleDownload}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Download HTML"
          >
            💾 Download
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <HtmlPreview content={artifact.content} ref={iframeRef} />
        ) : (
          <div className="h-full overflow-auto">
            <pre className="p-4 text-sm font-mono text-neutral-100 whitespace-pre-wrap">
              <code className="language-html">
                {artifact.content}
              </code>
            </pre>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          {artifact.content.length} characters • {artifact.content.split('\n').length} lines
        </div>
        <div>
          HTML Document
        </div>
      </div>
    </div>
  )
}

// HTML Preview component
interface HtmlPreviewProps {
  content: string
}

const HtmlPreview = React.forwardRef<HTMLIFrameElement, HtmlPreviewProps>(
  ({ content }, ref) => {
    const [iframeLoaded, setIframeLoaded] = React.useState(false)
    const [error, setError] = React.useState<string | null>(null)

    React.useEffect(() => {
      if (ref && 'current' in ref && ref.current) {
        try {
          const iframe = ref.current
          const blob = new Blob([content], { type: 'text/html' })
          const url = URL.createObjectURL(blob)
          
          iframe.src = url
          iframe.onload = () => {
            setIframeLoaded(true)
            setError(null)
            URL.revokeObjectURL(url)
          }
          
          iframe.onerror = () => {
            setError('Failed to load HTML content')
            setIframeLoaded(false)
            URL.revokeObjectURL(url)
          }
        } catch (err) {
          setError('Failed to create HTML preview')
          setIframeLoaded(false)
        }
      }
    }, [content, ref])

    return (
      <div className="h-full relative bg-white">
        {!iframeLoaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-800 text-neutral-400">
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <div>Loading preview...</div>
            </div>
          </div>
        )}
        
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-800 text-red-400">
            <div className="text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <div className="text-lg font-medium mb-2">Preview Error</div>
              <div className="text-sm">{error}</div>
            </div>
          </div>
        )}
        
        <iframe
          ref={ref}
          className="w-full h-full border-none"
          sandbox="allow-scripts allow-same-origin allow-forms"
          title="HTML Preview"
        />
      </div>
    )
  }
)
