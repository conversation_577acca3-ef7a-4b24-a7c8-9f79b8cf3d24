import React, { useState, useRef, useEffect } from 'react'
import { Artifact } from '../../../types'

interface MermaidArtifactViewerProps {
  artifact: Artifact
}

export function MermaidArtifactViewer({ artifact }: MermaidArtifactViewerProps) {
  const [viewMode, setViewMode] = useState<'diagram' | 'source'>('diagram')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const diagramRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (viewMode === 'diagram') {
      renderMermaidDiagram()
    }
  }, [artifact.content, viewMode])

  const renderMermaidDiagram = async () => {
    if (!diagramRef.current) return

    setIsLoading(true)
    setError(null)

    try {
      // For now, we'll show a placeholder since we don't have mermaid library
      // In a real implementation, you'd use: import mermaid from 'mermaid'
      
      // Simulate loading
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Create a simple SVG placeholder
      const svgContent = createMermaidPlaceholder(artifact.content)
      diagramRef.current.innerHTML = svgContent
      
      setIsLoading(false)
    } catch (err) {
      setError('Failed to render Mermaid diagram')
      setIsLoading(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      console.log('Mermaid diagram copied to clipboard')
    } catch (error) {
      console.error('Failed to copy diagram:', error)
    }
  }

  const handleDownload = () => {
    const blob = new Blob([artifact.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.mmd`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportSvg = () => {
    // In a real implementation, you'd export the rendered SVG
    console.log('Export SVG functionality would be implemented here')
  }

  return (
    <div className="h-full flex flex-col bg-neutral-900">
      {/* Toolbar */}
      <div className="flex-shrink-0 flex items-center justify-between p-3 bg-neutral-800 border-b border-neutral-700">
        <div className="flex items-center space-x-3">
          {/* View mode toggle */}
          <div className="flex bg-neutral-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('diagram')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'diagram'
                  ? 'bg-indigo-600 text-white'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              📊 Diagram
            </button>
            <button
              onClick={() => setViewMode('source')}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                viewMode === 'source'
                  ? 'bg-indigo-600 text-white'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              📝 Source
            </button>
          </div>

          <span className="text-xs text-neutral-500">
            Mermaid
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Copy button */}
          <button
            onClick={handleCopy}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Copy source"
          >
            📋 Copy
          </button>

          {/* Export SVG button */}
          <button
            onClick={handleExportSvg}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Export as SVG"
          >
            🖼️ SVG
          </button>

          {/* Download button */}
          <button
            onClick={handleDownload}
            className="px-3 py-1.5 text-sm bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Download source"
          >
            💾 Download
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'diagram' ? (
          <div className="h-full flex items-center justify-center p-6">
            {isLoading ? (
              <div className="text-center text-neutral-400">
                <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                <div>Rendering diagram...</div>
              </div>
            ) : error ? (
              <div className="text-center text-red-400">
                <div className="text-4xl mb-4">⚠️</div>
                <div className="text-lg font-medium mb-2">Diagram Error</div>
                <div className="text-sm">{error}</div>
              </div>
            ) : (
              <div 
                ref={diagramRef}
                className="max-w-full max-h-full overflow-auto bg-white rounded-lg p-4"
              />
            )}
          </div>
        ) : (
          <div className="h-full">
            <pre className="p-4 text-sm font-mono text-neutral-100 whitespace-pre-wrap h-full overflow-auto">
              {artifact.content}
            </pre>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          {artifact.content.split('\n').length} lines
        </div>
        <div>
          Mermaid Diagram
        </div>
      </div>
    </div>
  )
}

// Helper function to create a placeholder for Mermaid diagrams
function createMermaidPlaceholder(content: string): string {
  // Analyze the content to determine diagram type
  const diagramType = detectMermaidType(content)
  
  return `
    <div class="flex items-center justify-center h-64 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg">
      <div class="text-center text-gray-600">
        <div class="text-4xl mb-4">📊</div>
        <div class="text-lg font-medium mb-2">${diagramType} Diagram</div>
        <div class="text-sm">Mermaid rendering would appear here</div>
        <div class="text-xs mt-2 text-gray-500">
          In production, this would use the Mermaid library
        </div>
      </div>
    </div>
  `
}

function detectMermaidType(content: string): string {
  const firstLine = content.trim().split('\n')[0].toLowerCase()
  
  if (firstLine.includes('flowchart') || firstLine.includes('graph')) {
    return 'Flowchart'
  } else if (firstLine.includes('sequencediagram')) {
    return 'Sequence'
  } else if (firstLine.includes('classDiagram')) {
    return 'Class'
  } else if (firstLine.includes('stateDiagram')) {
    return 'State'
  } else if (firstLine.includes('erDiagram')) {
    return 'Entity Relationship'
  } else if (firstLine.includes('gantt')) {
    return 'Gantt'
  } else if (firstLine.includes('pie')) {
    return 'Pie Chart'
  } else {
    return 'Mermaid'
  }
}
