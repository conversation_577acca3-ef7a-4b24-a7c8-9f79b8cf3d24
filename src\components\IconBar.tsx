import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAppStore } from '../store'
import Chat<PERSON>o<PERSON>ogo from './ChatLoLogo'

interface IconBarProps {
  className?: string
}

const IconBar: React.FC<IconBarProps> = ({ className = '' }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { sidebarOpen, setSidebarOpen, activeIconBarItem, setActiveIconBarItem } = useAppStore()

  const navigationItems = [
    {
      id: 'home',
      icon: 'fa-solid fa-home',
      label: 'Home',
      path: '/',
      isActive: location.pathname === '/'
    },
    {
      id: 'chat',
      icon: 'fa-solid fa-comment',
      label: 'Chat',
      path: '/',
      isActive: location.pathname === '/',
      isDefault: true
    },
    {
      id: 'history',
      icon: 'fa-solid fa-clock-rotate-left',
      label: 'History',
      path: '/history',
      isActive: location.pathname === '/history'
    },
    {
      id: 'files',
      icon: 'fa-solid fa-folder-tree',
      label: 'Files',
      path: '/files',
      isActive: location.pathname === '/files',
      disabled: true // Future feature
    }
  ]

  const bottomItems = [
    {
      id: 'settings',
      icon: 'fa-solid fa-gear',
      label: 'Settings',
      path: '/settings',
      isActive: location.pathname === '/settings'
    },
    {
      id: 'user',
      icon: 'fa-solid fa-user',
      label: 'Profile',
      path: '/profile',
      isActive: location.pathname === '/profile',
      disabled: true // Future feature
    }
  ]

  const handleNavigation = (path: string, itemId: string, disabled?: boolean) => {
    if (disabled) return
    setActiveIconBarItem(itemId)
    navigate(path)
  }

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <div className={`w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2 h-full ${className}`}>
      {/* Logo Section */}
      <div className="mb-4">
        <button
          onClick={() => navigate('/')}
          className="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors group relative"
          title="ChatLo"
        >
          <ChatLoLogo variant="icon" size="sm" />
          <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
            ChatLo
          </div>
        </button>
      </div>

      {/* Top Navigation Icons */}
      <div className="flex flex-col gap-1 mb-auto">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => handleNavigation(item.path, item.id, item.disabled)}
            disabled={item.disabled}
            className={`
              u1-nav-icon group relative
              ${activeIconBarItem === item.id ? 'bg-primary/20 border-l-2 border-primary' : ''}
              ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            title={item.label}
          >
            <i className={`${item.icon} ${activeIconBarItem === item.id ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.label}
              {item.disabled && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>

      {/* Sidebar Toggle (Mobile) */}
      <div className="md:hidden mb-2">
        <button
          onClick={handleToggleSidebar}
          className="u1-nav-icon group relative"
          title="Toggle Sidebar"
        >
          <i className="fa-solid fa-bars text-supplement1"></i>
          <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
            Toggle Sidebar
          </div>
        </button>
      </div>

      {/* Bottom Navigation Icons */}
      <div className="flex flex-col gap-1">
        {bottomItems.map((item) => (
          <button
            key={item.id}
            onClick={() => handleNavigation(item.path, item.id, item.disabled)}
            disabled={item.disabled}
            className={`
              u1-nav-icon group relative
              ${activeIconBarItem === item.id ? 'bg-primary/20 border-l-2 border-primary' : ''}
              ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            title={item.label}
          >
            <i className={`${item.icon} ${activeIconBarItem === item.id ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.label}
              {item.disabled && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>

      {/* Connection Status Indicator */}
      <div className="mt-2">
        <div className="w-2 h-2 bg-green-400 rounded-full" title="Online"></div>
      </div>
    </div>
  )
}

export default IconBar
