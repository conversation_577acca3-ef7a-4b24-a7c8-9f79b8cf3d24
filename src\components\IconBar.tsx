import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAppStore } from '../store'
import Chat<PERSON>o<PERSON>ogo from './ChatLoLogo'

interface IconBarProps {
  className?: string
}

const IconBar: React.FC<IconBarProps> = ({ className = '' }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { activeIconBarItem, setActiveIconBarItem } = useAppStore()

  // Top navigation items - matching design reference exactly
  const topNavigationItems = [
    {
      id: 'home',
      icon: 'fa-solid fa-home',
      label: 'Home',
      path: '/',
      isMock: true // Mock icon for future feature
    },
    {
      id: 'chat',
      icon: 'fa-solid fa-comment',
      label: 'Chat',
      path: '/',
      isMock: false // Active feature
    },
    {
      id: 'history',
      icon: 'fa-solid fa-clock-rotate-left',
      label: 'History',
      path: '/history',
      isMock: false // Active feature
    },
    {
      id: 'files',
      icon: 'fa-solid fa-folder-tree',
      label: 'Files',
      path: '/files',
      isMock: true // Mock icon for future feature
    }
  ]

  // Bottom navigation items - matching design reference exactly
  const bottomNavigationItems = [
    {
      id: 'user',
      icon: 'fa-solid fa-user',
      label: 'Profile',
      path: '/profile',
      isMock: true // Mock icon for future feature
    },
    {
      id: 'settings',
      icon: 'fa-solid fa-gear',
      label: 'Settings',
      path: '/settings',
      isMock: false // Active feature
    }
  ]

  const handleNavigation = (path: string, itemId: string, isMock?: boolean) => {
    if (isMock) return // Don't navigate for mock icons
    setActiveIconBarItem(itemId)
    navigate(path)
  }

  // Determine active item based on current route
  const getActiveItem = () => {
    if (location.pathname === '/') return 'chat'
    if (location.pathname === '/history') return 'history'
    if (location.pathname === '/settings') return 'settings'
    return 'chat' // Default to chat
  }

  const currentActiveItem = getActiveItem()

  return (
    <div className={`w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2 h-full ${className} hidden md:flex`}>

      {/* Logo Section */}
      <div className="mb-4">
        <button
          onClick={() => handleNavigation('/', 'chat', false)}
          className="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors group relative"
          title="ChatLo"
        >
          <ChatLoLogo variant="icon" size="sm" />
          <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
            ChatLo
          </div>
        </button>
      </div>

      {/* Top Navigation Icons */}
      <div className="flex flex-col gap-1 mb-auto">
        {topNavigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => handleNavigation(item.path, item.id, item.isMock)}
            className={`
              w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative
              ${currentActiveItem === item.id ? 'bg-primary/20 border-l-2 border-primary' : ''}
              ${item.isMock ? 'opacity-70' : ''}
            `}
            title={item.label}
          >
            <i className={`${item.icon} ${currentActiveItem === item.id ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.label}
              {item.isMock && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>

      {/* Bottom Navigation Icons */}
      <div className="flex flex-col gap-1">
        {bottomNavigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => handleNavigation(item.path, item.id, item.isMock)}
            className={`
              w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative
              ${currentActiveItem === item.id ? 'bg-primary/20 border-l-2 border-primary' : ''}
              ${item.isMock ? 'opacity-70' : ''}
            `}
            title={item.label}
          >
            <i className={`${item.icon} ${currentActiveItem === item.id ? 'text-primary' : 'text-supplement1'}`}></i>
            <div className="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {item.label}
              {item.isMock && ' (Coming Soon)'}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default IconBar
