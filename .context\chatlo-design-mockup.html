<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatLo - AI Chat Application</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .chat-bubble { @apply rounded-lg px-4 py-2 text-sm max-w-md; }
        .chat-bubble-user { @apply bg-indigo-500 text-white; }
        .chat-bubble-assistant { @apply bg-neutral-800 text-neutral-100; }
        .sidebar-item { @apply flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-neutral-800 focus:bg-neutral-800 transition-colors; }
        .input-field { @apply bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none; }
        .btn-primary { @apply bg-indigo-500 hover:bg-indigo-600 text-white font-medium px-4 py-2 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-neutral-800 hover:bg-neutral-700 text-neutral-100 font-medium px-4 py-2 rounded-lg transition-colors; }
        .btn-ghost { @apply hover:bg-neutral-800 text-neutral-100 p-2 rounded-lg transition-colors; }
        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: transparent; }
        ::-webkit-scrollbar-thumb { background: #525252; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #737373; }
        ::selection { background: rgba(99, 102, 241, 0.6); }
    </style>
</head>
<body class="h-full bg-neutral-950 text-neutral-100 antialiased selection:bg-indigo-500/60 overflow-hidden">
    <div class="h-screen flex">
        <!-- Mobile menu button -->
        <div class="md:hidden fixed top-4 left-4 z-50">
            <button class="h-8 w-8 flex items-center justify-center rounded hover:bg-neutral-800 bg-neutral-900/80 backdrop-blur-sm">
                <i data-lucide="menu" class="h-4 w-4"></i>
            </button>
        </div>

        <!-- Sidebar -->
        <aside class="flex flex-col w-64 bg-neutral-900/60 backdrop-blur-lg border-r border-neutral-800 h-screen">
            <!-- Header -->
            <header class="flex items-center gap-2 h-16 px-6 border-b border-neutral-800">
                <div class="flex items-center justify-center">
                    <div class="h-11 w-11 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">CL</span>
                    </div>
                    <span class="ml-2 text-lg font-semibold">ChatLo</span>
                </div>
            </header>

            <!-- New Conversation Button -->
            <div class="p-4">
                <button class="w-full flex items-center gap-2 px-4 py-3 bg-indigo-500 hover:bg-indigo-600 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="plus" class="h-4 w-4"></i>
                    New Conversation
                </button>
            </div>

            <!-- Conversations List -->
            <div class="flex-1 overflow-y-auto">
                <div class="px-4 pb-4 space-y-1">
                    <div class="sidebar-item bg-neutral-800 rounded-lg">
                        <i data-lucide="message-circle" class="h-4 w-4 text-neutral-400"></i>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium truncate">OpenRouter Integration</div>
                            <div class="text-xs text-neutral-500 truncate">How to integrate with OpenRouter API...</div>
                        </div>
                    </div>
                    <div class="sidebar-item rounded-lg">
                        <i data-lucide="message-circle" class="h-4 w-4 text-neutral-400"></i>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium truncate">React Components</div>
                            <div class="text-xs text-neutral-500 truncate">Building reusable components...</div>
                        </div>
                    </div>
                    <div class="sidebar-item rounded-lg">
                        <i data-lucide="message-circle" class="h-4 w-4 text-neutral-400"></i>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium truncate">Database Design</div>
                            <div class="text-xs text-neutral-500 truncate">SQLite schema for conversations...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="p-4 border-t border-neutral-800">
                <div class="flex items-center justify-between text-xs text-neutral-500">
                    <span>v1.0.0</span>
                    <button class="btn-ghost p-1" onclick="toggleSettings()">
                        <i data-lucide="settings" class="h-4 w-4"></i>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="flex-1 flex flex-col">
            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto p-6">
                <div class="max-w-4xl mx-auto space-y-6">
                    <!-- Welcome Message -->
                    <div class="text-center py-12">
                        <div class="h-16 w-16 rounded-full bg-indigo-500/10 flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="bot" class="h-8 w-8 text-indigo-500"></i>
                        </div>
                        <h2 class="text-xl font-semibold mb-2">Welcome to ChatLo</h2>
                        <p class="text-neutral-400 mb-6 max-w-md mx-auto">
                            Start a new conversation to begin chatting with AI models through OpenRouter.
                        </p>
                    </div>

                    <!-- Sample Messages -->
                    <div class="space-y-6">
                        <!-- User Message -->
                        <div class="flex gap-4 justify-end">
                            <div class="flex flex-col items-end max-w-2xl">
                                <div class="group relative">
                                    <div class="chat-bubble chat-bubble-user">
                                        How do I integrate OpenRouter with my React application?
                                    </div>
                                    <button class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-black/20 text-xs">
                                        <i data-lucide="copy" class="h-3 w-3"></i>
                                    </button>
                                </div>
                                <div class="flex items-center gap-2 text-xs text-neutral-500 mt-1">
                                    <span>2:34 PM</span>
                                </div>
                            </div>
                            <div class="h-8 w-8 rounded-full bg-neutral-700 flex items-center justify-center shrink-0">
                                <i data-lucide="user" class="h-4 w-4 text-neutral-300"></i>
                            </div>
                        </div>

                        <!-- Assistant Message -->
                        <div class="flex gap-4">
                            <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center shrink-0">
                                <i data-lucide="bot" class="h-4 w-4 text-white"></i>
                            </div>
                            <div class="flex flex-col max-w-2xl">
                                <div class="group relative">
                                    <div class="chat-bubble chat-bubble-assistant">
                                        To integrate OpenRouter with your React application, you'll need to:
                                        
                                        1. Install the OpenRouter SDK
                                        2. Set up your API key
                                        3. Create a service layer for API calls
                                        4. Implement streaming for real-time responses
                                        
                                        Here's a basic example of how to get started...
                                    </div>
                                    <button class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-black/20 text-xs">
                                        <i data-lucide="copy" class="h-3 w-3"></i>
                                    </button>
                                </div>
                                <div class="flex items-center gap-2 text-xs text-neutral-500 mt-1">
                                    <span>2:34 PM</span>
                                    <span>•</span>
                                    <span class="text-indigo-400">gpt-4-turbo</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="border-t border-neutral-800 p-6">
                <form class="max-w-4xl mx-auto">
                    <div class="flex gap-3 items-end">
                        <!-- Settings Button -->
                        <button type="button" class="h-10 w-10 flex items-center justify-center rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors shrink-0" onclick="toggleSettings()">
                            <i data-lucide="settings" class="h-4 w-4"></i>
                        </button>

                        <!-- Input Field -->
                        <div class="flex-1 relative">
                            <textarea 
                                placeholder="Type your message… (Shift+Enter for new line)"
                                class="w-full bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 pr-12 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none resize-none min-h-[44px] max-h-32"
                                rows="1"
                            ></textarea>
                        </div>

                        <!-- Send Button -->
                        <button type="submit" class="h-10 w-10 flex items-center justify-center rounded-lg bg-indigo-500 hover:bg-indigo-600 text-white transition-colors shrink-0">
                            <i data-lucide="send" class="h-4 w-4"></i>
                        </button>
                    </div>

                    <!-- Model Info -->
                    <div class="mt-2 flex items-center justify-between text-xs text-neutral-500">
                        <div class="flex items-center gap-4">
                            <span>Model: gpt-4-turbo</span>
                            <span>•</span>
                            <span>Temp: 0.7</span>
                            <span>•</span>
                            <span>Max: 4K</span>
                            <span>•</span>
                            <span>Top-P: 0.95</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span>Shift+Enter for new line</span>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <!-- Settings Modal Overlay -->
    <div id="settingsModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center hidden">
        <div class="bg-neutral-900 border border-neutral-700 rounded-xl shadow-2xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-neutral-700">
                <div class="flex items-center gap-2">
                    <i data-lucide="settings" class="h-5 w-5 text-indigo-400"></i>
                    <h2 class="text-lg font-semibold">Settings</h2>
                </div>
                <button onclick="toggleSettings()" class="p-2 hover:bg-neutral-800 rounded-lg transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6 space-y-6 overflow-y-auto max-h-[60vh]">
                <!-- API Configuration Section -->
                <div class="space-y-4">
                    <h3 class="text-sm font-medium text-neutral-300 flex items-center gap-2">
                        <i data-lucide="key" class="h-4 w-4"></i>
                        API Configuration
                    </h3>

                    <!-- OpenRouter API Key -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-neutral-400">
                            OpenRouter API Key
                        </label>
                        <div class="relative">
                            <input
                                type="password"
                                placeholder="sk-or-v1-..."
                                class="input-field w-full pr-10"
                                value="sk-or-v1-••••••••••••••••••••••••••••••••"
                            >
                            <button class="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-neutral-800 rounded">
                                <i data-lucide="eye" class="h-4 w-4 text-neutral-500"></i>
                            </button>
                        </div>
                        <p class="text-xs text-neutral-500">
                            Get your API key from <a href="#" class="text-indigo-400 hover:text-indigo-300">OpenRouter</a>
                        </p>
                    </div>
                </div>

                <!-- Placeholder for Future Settings -->
                <div class="space-y-4">
                    <h3 class="text-sm font-medium text-neutral-300 flex items-center gap-2">
                        <i data-lucide="sliders" class="h-4 w-4"></i>
                        General Settings
                    </h3>

                    <div class="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4 text-center">
                        <i data-lucide="plus-circle" class="h-8 w-8 text-neutral-500 mx-auto mb-2"></i>
                        <p class="text-sm text-neutral-400">More settings coming soon</p>
                        <p class="text-xs text-neutral-500 mt-1">Theme, notifications, and more</p>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end gap-3 p-6 border-t border-neutral-700">
                <button onclick="toggleSettings()" class="btn-secondary">
                    Cancel
                </button>
                <button class="btn-primary">
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Settings modal functionality
        function toggleSettings() {
            const modal = document.getElementById('settingsModal');
            const isHidden = modal.classList.contains('hidden');

            if (isHidden) {
                modal.classList.remove('hidden');
                // Re-initialize icons for the modal content
                setTimeout(() => lucide.createIcons(), 10);
            } else {
                modal.classList.add('hidden');
            }
        }

        // Simple interactivity for demo
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const menuBtn = document.querySelector('[data-lucide="menu"]').parentElement;
            const sidebar = document.querySelector('aside');

            menuBtn.addEventListener('click', function() {
                sidebar.classList.toggle('-translate-x-full');
            });

            // Auto-resize textarea
            const textarea = document.querySelector('textarea');
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 128) + 'px';
            });

            // Close modal when clicking outside
            document.getElementById('settingsModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    toggleSettings();
                }
            });

            // Toggle password visibility
            document.querySelector('[data-lucide="eye"]').parentElement.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const icon = this.querySelector('[data-lucide]');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.setAttribute('data-lucide', 'eye-off');
                } else {
                    input.type = 'password';
                    icon.setAttribute('data-lucide', 'eye');
                }
                lucide.createIcons();
            });
        });
    </script>
</body>
</html>
