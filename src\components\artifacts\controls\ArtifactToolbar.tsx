import React from 'react'
import { useAppStore } from '../../../store'
import { useArtifactToasts } from './ArtifactToast'

export function ArtifactToolbar() {
  const {
    artifacts: { isFullscreen, currentArtifact },
    closeArtifacts,
    toggleArtifactsFullscreen
  } = useAppStore()

  const { success, error } = useArtifactToasts()

  const handleCopyContent = async () => {
    if (!currentArtifact) return

    try {
      await navigator.clipboard.writeText(currentArtifact.content)
      success('Content copied to clipboard')
    } catch (err) {
      console.error('Failed to copy content:', err)
      error('Failed to copy content')
    }
  }

  const handleDownload = () => {
    if (!currentArtifact) return

    try {
      const blob = new Blob([currentArtifact.content], {
        type: getContentType(currentArtifact.type)
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = getFileName(currentArtifact)
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      success('File downloaded successfully')
    } catch (err) {
      console.error('Failed to download file:', err)
      error('Failed to download file')
    }
  }

  return (
    <div className="flex items-center justify-between p-3 bg-neutral-800">
      {/* Left side - Title */}
      <div className="flex items-center space-x-3">
        <div className="text-lg font-medium text-white">
          Artifacts
        </div>
        {currentArtifact && (
          <div className="text-sm text-neutral-400">
            • {currentArtifact.title}
          </div>
        )}
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-2">
        {/* Copy button */}
        {currentArtifact && (
          <button
            onClick={handleCopyContent}
            className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
            title="Copy content"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
        )}

        {/* Download button */}
        {currentArtifact && (
          <button
            onClick={handleDownload}
            className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
            title="Download"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        )}

        {/* Fullscreen toggle */}
        <button
          onClick={toggleArtifactsFullscreen}
          className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0 0l5.5 5.5" />
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          )}
        </button>

        {/* Close button */}
        <button
          onClick={closeArtifacts}
          className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
          title="Close artifacts"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  )
}

// Helper functions
function getContentType(artifactType: string): string {
  switch (artifactType) {
    case 'code':
      return 'text/plain'
    case 'json':
      return 'application/json'
    case 'html':
      return 'text/html'
    case 'markdown':
      return 'text/markdown'
    case 'mermaid':
      return 'text/plain'
    case 'image':
      return 'image/png'
    default:
      return 'text/plain'
  }
}

function getFileName(artifact: any): string {
  const extension = getFileExtension(artifact.type, artifact.metadata?.language)
  const baseName = artifact.title.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()
  return `${baseName}.${extension}`
}

function getFileExtension(type: string, language?: string): string {
  switch (type) {
    case 'code':
      return getCodeExtension(language)
    case 'json':
      return 'json'
    case 'html':
      return 'html'
    case 'markdown':
      return 'md'
    case 'mermaid':
      return 'mmd'
    case 'image':
      return 'png'
    default:
      return 'txt'
  }
}

function getCodeExtension(language?: string): string {
  const extensions: Record<string, string> = {
    javascript: 'js',
    typescript: 'ts',
    python: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    csharp: 'cs',
    php: 'php',
    ruby: 'rb',
    go: 'go',
    rust: 'rs',
    swift: 'swift',
    kotlin: 'kt',
    scala: 'scala',
    sql: 'sql',
    bash: 'sh',
    powershell: 'ps1',
    yaml: 'yml',
    xml: 'xml',
    css: 'css',
    scss: 'scss',
    less: 'less'
  }
  
  return extensions[language?.toLowerCase() || ''] || 'txt'
}
